# =================================================================================
#  滑轨位置记忆模块 - 门.py
#  功能：记录和管理滑轨的特定位置点，支持快速定位
# =================================================================================
import customtkinter as ctk
import json
import os

class ConveyorPositionManager:
    """滑轨位置记忆管理器"""

    def __init__(self, parent_app):
        """
        初始化位置管理器
        :param parent_app: 主程序实例，用于获取当前位置和控制滑轨移动
        """
        self.parent_app = parent_app
        self.positions_file = "conveyor_positions.json"

        # 预定义的位置名称
        self.position_names = [
            "检测区一",
            "检测区二",
            "装配区一",
            "装配区二"
        ]

        # 位置数据存储 {位置名称: 位置值(mm)}
        self.saved_positions = {}

        # 加载已保存的位置
        self.load_positions()

        # UI组件引用
        self.position_frame = None
        self.position_labels = {}
        self.move_buttons = {}
        self.record_buttons = {}

    def create_position_ui(self, parent_frame):
        """创建位置管理UI界面"""
        # 主框架
        self.position_frame = ctk.CTkFrame(parent_frame)
        self.position_frame.pack(pady=10, padx=10, fill="x")

        # 标题
        title_label = ctk.CTkLabel(
            self.position_frame,
            text="滑轨位置记忆",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=10)

        # 为每个位置创建控制行
        for position_name in self.position_names:
            self._create_position_row(position_name)

        # 底部操作按钮
        bottom_frame = ctk.CTkFrame(self.position_frame, fg_color="transparent")
        bottom_frame.pack(pady=10, padx=5, fill="x")

        # 清除所有位置按钮
        clear_all_btn = ctk.CTkButton(
            bottom_frame,
            text="清除所有记录",
            command=self.clear_all_positions,
            fg_color="red",
            hover_color="darkred"
        )
        clear_all_btn.pack(side="left", padx=5)

        # 保存位置到文件按钮
        save_btn = ctk.CTkButton(
            bottom_frame,
            text="保存到文件",
            command=self.save_positions
        )
        save_btn.pack(side="right", padx=5)

    def _create_position_row(self, position_name):
        """为单个位置创建控制行"""
        # 行框架
        row_frame = ctk.CTkFrame(self.position_frame, fg_color="transparent")
        row_frame.pack(pady=5, padx=5, fill="x")

        # 位置名称标签
        name_label = ctk.CTkLabel(
            row_frame,
            text=position_name,
            font=ctk.CTkFont(size=12, weight="bold"),
            width=80
        )
        name_label.pack(side="left", padx=5)

        # 位置值显示标签
        position_label = ctk.CTkLabel(
            row_frame,
            text="未记录",
            font=ctk.CTkFont(size=11),
            width=100,
            fg_color="gray30"
        )
        position_label.pack(side="left", padx=5)
        self.position_labels[position_name] = position_label

        # 记录当前位置按钮
        record_btn = ctk.CTkButton(
            row_frame,
            text="记录",
            width=60,
            command=lambda name=position_name: self.record_current_position(name)
        )
        record_btn.pack(side="left", padx=5)
        self.record_buttons[position_name] = record_btn

        # 移动到此位置按钮
        move_btn = ctk.CTkButton(
            row_frame,
            text="移动到此",
            width=80,
            command=lambda name=position_name: self.move_to_position(name),
            state="disabled"  # 初始状态为禁用
        )
        move_btn.pack(side="left", padx=5)
        self.move_buttons[position_name] = move_btn

        # 删除此位置按钮
        delete_btn = ctk.CTkButton(
            row_frame,
            text="删除",
            width=50,
            command=lambda name=position_name: self.delete_position(name),
            fg_color="orange",
            hover_color="darkorange"
        )
        delete_btn.pack(side="right", padx=5)

        # 更新显示
        self._update_position_display(position_name)

    def record_current_position(self, position_name):
        """记录当前滑轨位置"""
        if not self.parent_app.is_robot_connected:
            self.parent_app.log("⚠️ 请先连接机器人才能记录位置", "orange")
            return

        current_pos = self.parent_app.current_conveyor_position
        self.saved_positions[position_name] = current_pos

        # 更新UI显示
        self._update_position_display(position_name)

        # 记录日志
        self.parent_app.log(f"📍 已记录 {position_name}: {current_pos:.2f} mm", "green")

        # 自动保存到文件
        self.save_positions()

    def move_to_position(self, position_name):
        """移动滑轨到指定记录位置"""
        if not self.parent_app.is_robot_connected:
            self.parent_app.log("⚠️ 请先连接机器人才能移动滑轨", "orange")
            return

        if position_name not in self.saved_positions:
            self.parent_app.log(f"❌ 位置 {position_name} 尚未记录", "red")
            return

        target_position = self.saved_positions[position_name]
        current_position = self.parent_app.current_conveyor_position
        distance_to_move = target_position - current_position

        if abs(distance_to_move) < 0.1:  # 如果距离很小，认为已经在目标位置
            self.parent_app.log(f"✅ 滑轨已在 {position_name} 位置", "green")
            return

        self.parent_app.log(f"🎯 正在移动到 {position_name} ({target_position:.2f} mm)...")

        # 使用MovJExt命令移动滑轨
        cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"

        # 导入send_cmd函数
        from pro import send_cmd
        if send_cmd(self.parent_app.motion_socket, cmd, "MOT"):
            # 更新当前位置
            self.parent_app.current_conveyor_position = target_position
            self.parent_app.update_conveyor_display()
            self.parent_app.log(f"✅ 已到达 {position_name} 位置: {target_position:.2f} mm", "green")
        else:
            self.parent_app.log(f"❌ 移动到 {position_name} 失败", "red")

    def delete_position(self, position_name):
        """删除指定位置记录"""
        if position_name in self.saved_positions:
            del self.saved_positions[position_name]
            self._update_position_display(position_name)
            self.parent_app.log(f"🗑️ 已删除位置记录: {position_name}", "yellow")
            self.save_positions()
        else:
            self.parent_app.log(f"⚠️ 位置 {position_name} 未找到记录", "orange")

    def clear_all_positions(self):
        """清除所有位置记录"""
        self.saved_positions.clear()
        for position_name in self.position_names:
            self._update_position_display(position_name)
        self.parent_app.log("🗑️ 已清除所有位置记录", "yellow")
        self.save_positions()

    def _update_position_display(self, position_name):
        """更新指定位置的UI显示"""
        if position_name in self.saved_positions:
            # 有记录的位置
            position_value = self.saved_positions[position_name]
            self.position_labels[position_name].configure(
                text=f"{position_value:.2f} mm",
                fg_color="green"
            )
            self.move_buttons[position_name].configure(state="normal")
        else:
            # 无记录的位置
            self.position_labels[position_name].configure(
                text="未记录",
                fg_color="gray30"
            )
            self.move_buttons[position_name].configure(state="disabled")

    def save_positions(self):
        """保存位置数据到文件"""
        try:
            with open(self.positions_file, 'w', encoding='utf-8') as f:
                json.dump(self.saved_positions, f, ensure_ascii=False, indent=2)
            self.parent_app.log("💾 位置数据已保存到文件", "cyan")
        except Exception as e:
            self.parent_app.log(f"❌ 保存位置数据失败: {e}", "red")

    def load_positions(self):
        """从文件加载位置数据"""
        try:
            if os.path.exists(self.positions_file):
                with open(self.positions_file, 'r', encoding='utf-8') as f:
                    self.saved_positions = json.load(f)
                print(f"✅ 已加载 {len(self.saved_positions)} 个位置记录")
            else:
                print("📁 位置记录文件不存在，将创建新文件")
        except Exception as e:
            print(f"❌ 加载位置数据失败: {e}")
            self.saved_positions = {}

    def get_position_info(self):
        """获取所有位置信息（用于调试或其他模块调用）"""
        return self.saved_positions.copy()