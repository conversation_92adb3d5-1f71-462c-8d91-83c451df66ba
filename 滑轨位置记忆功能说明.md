# 滑轨位置记忆功能说明

## 功能概述

滑轨位置记忆功能是在原有机器人控制系统基础上新增的V17.0功能，允许用户保存和快速定位到特定的滑轨位置。

## 主要功能

### 1. 位置记录
- **检测区一**: 记录检测工位1的滑轨位置
- **检测区二**: 记录检测工位2的滑轨位置  
- **装配区一**: 记录装配工位1的滑轨位置
- **装配区二**: 记录装配工位2的滑轨位置

### 2. 快速定位
- 点击"移动到此"按钮，滑轨自动移动到已记录的位置
- 支持精确定位，误差小于0.1mm时认为已到达目标位置

### 3. 位置管理
- **记录**: 保存当前滑轨位置到指定位置点
- **删除**: 删除单个位置记录
- **清除所有记录**: 一键清空所有位置记录
- **自动保存**: 位置数据自动保存到文件，程序重启后自动加载

## 使用方法

### 记录位置
1. 使用传送带控制功能将滑轨移动到目标位置
2. 点击对应位置的"记录"按钮
3. 系统会保存当前位置并显示在界面上

### 移动到记录位置
1. 确保机器人已连接
2. 点击目标位置的"移动到此"按钮
3. 滑轨将自动移动到记录的位置

### 位置管理
- **删除位置**: 点击"删除"按钮移除单个位置记录
- **清除所有**: 点击"清除所有记录"按钮移除所有位置
- **保存到文件**: 点击"保存到文件"按钮手动保存位置数据

## 界面说明

### 位置显示
- **绿色背景**: 已记录位置，显示具体数值(mm)
- **灰色背景**: 未记录位置，显示"未记录"
- **按钮状态**: 已记录位置的"移动到此"按钮为可用状态

### 操作按钮
- **记录**: 保存当前滑轨位置
- **移动到此**: 移动到已记录位置（需要机器人连接）
- **删除**: 删除该位置记录
- **清除所有记录**: 删除所有位置记录
- **保存到文件**: 手动保存位置数据

## 文件说明

### 核心文件
- `门.py`: 位置记忆功能模块
- `pro.py`: 主程序（已集成位置记忆功能）
- `conveyor_positions.json`: 位置数据存储文件（自动生成）

### 测试文件
- `test_position_manager.py`: 位置记忆功能测试程序
- `滑轨位置记忆功能说明.md`: 本说明文档

## 注意事项

1. **机器人连接**: 记录和移动功能需要机器人处于连接状态
2. **位置精度**: 系统记录位置精度为0.01mm
3. **数据持久化**: 位置数据自动保存到JSON文件，程序重启后自动加载
4. **安全性**: 移动前会检查机器人连接状态，确保操作安全

## 错误处理

- 机器人未连接时会显示警告信息
- 移动到未记录位置时会显示错误提示
- 文件读写错误会在日志中显示详细信息

## 版本信息

- **版本**: V17.0
- **新增功能**: 滑轨位置记忆
- **兼容性**: 完全兼容V16.0的所有功能
