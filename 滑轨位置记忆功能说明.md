# 滑轨位置记忆功能说明

## 功能概述

滑轨位置记忆功能是在原有机器人控制系统基础上新增的V17.0功能，允许用户保存和快速定位到特定的滑轨位置。

## 位置系统说明

### 双位置系统
系统采用双位置跟踪机制：

1. **逻辑位置**: 可以通过"标定逻辑零点"重置为0，用于日常操作显示
2. **绝对位置**: 从真正的零点开始累积距离，不受标定影响，专门用于位置记录

### 位置记录原理
- **记录位置**: 保存的是绝对位置值，确保不受后续标定影响
- **移动到位置**: 基于绝对位置计算移动距离，保证精确定位
- **标定零点**: 只影响逻辑位置显示，不影响已记录的位置

## 主要功能

### 1. 位置记录
- **检测区一**: 记录检测工位1的滑轨位置
- **检测区二**: 记录检测工位2的滑轨位置  
- **装配区一**: 记录装配工位1的滑轨位置
- **装配区二**: 记录装配工位2的滑轨位置

### 2. 快速定位
- 点击"移动到此"按钮，滑轨自动移动到已记录的位置
- 支持精确定位，误差小于0.1mm时认为已到达目标位置

### 3. 位置管理
- **记录**: 保存当前滑轨位置到指定位置点
- **删除**: 删除单个位置记录
- **清除所有记录**: 一键清空所有位置记录
- **自动保存**: 位置数据自动保存到文件，程序重启后自动加载

## 使用方法

### 记录位置
1. 使用传送带控制功能将滑轨移动到目标位置
2. 点击对应位置的"记录"按钮
3. 系统会保存当前位置并显示在界面上

### 移动到记录位置
1. 确保机器人已连接
2. 点击目标位置的"移动到此"按钮
3. 滑轨将自动移动到记录的位置

### 位置管理
- **删除位置**: 点击"删除"按钮移除单个位置记录
- **清除所有**: 点击"清除所有记录"按钮移除所有位置
- **保存到文件**: 点击"保存到文件"按钮手动保存位置数据

## 界面说明

### 位置显示
- **绿色背景**: 已记录位置，显示具体数值(mm)
- **灰色背景**: 未记录位置，显示"未记录"
- **按钮状态**: 已记录位置的"移动到此"按钮为可用状态

### 操作按钮
- **记录**: 保存当前滑轨的绝对位置
- **移动到此**: 移动到已记录位置（需要机器人连接）
- **删除**: 删除该位置记录
- **清除所有记录**: 删除所有位置记录
- **保存到文件**: 手动保存位置数据
- **标定逻辑零点**: 将当前逻辑位置重置为0（不影响绝对位置和记录）
- **重置绝对零点**: 重置绝对位置为0（⚠️会影响所有已记录位置）

## 文件说明

### 核心文件
- `门.py`: 位置记忆功能模块
- `pro.py`: 主程序（已集成位置记忆功能）
- `conveyor_positions.json`: 位置数据存储文件（自动生成）

### 测试文件
- `test_position_manager.py`: 位置记忆功能测试程序
- `test_absolute_position.py`: 绝对位置系统测试程序
- `滑轨位置记忆功能说明.md`: 本说明文档

## 注意事项

1. **机器人连接**: 记录和移动功能需要机器人处于连接状态
2. **位置精度**: 系统记录位置精度为0.01mm
3. **数据持久化**: 位置数据自动保存到JSON文件，程序重启后自动加载
4. **安全性**: 移动前会检查机器人连接状态，确保操作安全

## 错误处理

- 机器人未连接时会显示警告信息
- 移动到未记录位置时会显示错误提示
- 文件读写错误会在日志中显示详细信息

## 界面滚动功能

### 滚动操作
- **鼠标滚轮**: 在控制面板或监控界面上使用鼠标滚轮上下滚动
- **滚动条**: 拖动右侧滚动条来快速定位到特定位置
- **键盘操作**: 使用上下箭头键进行滚动（需要先点击滚动区域获得焦点）

### 界面布局
- **左侧控制面板**: 包含所有控制功能，支持垂直滚动
- **右侧监控界面**: 包含图像显示和日志信息，支持垂直滚动
- **窗口尺寸**: 默认1200x900像素，可根据需要调整

### 滚动区域标识
- 滚动框架带有标题标签（"控制面板"、"监控界面"）
- 滚动条会在内容超出显示区域时自动出现
- 支持平滑滚动效果

## 版本信息

- **版本**: V17.0
- **新增功能**:
  - 滑轨位置记忆
  - 界面滚动功能
  - 窗口尺寸优化
- **兼容性**: 完全兼容V16.0的所有功能
