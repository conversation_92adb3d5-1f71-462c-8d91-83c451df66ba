# =================================================================================
#  机器人视觉控制系统 - V17.0 (集成滑轨位置记忆功能)
# =================================================================================
import customtkinter as ctk
import socket
import threading
import queue
import time
from PIL import Image, ImageTk
import os
import ctypes
from 门 import ConveyorPositionManager  # 导入位置记忆模块

# --- 1. 全局配置 ---
ROBOT_IP = "***********"
PYTHON_PC_IP = "*************"
DASHBOARD_PORT = 29999
MOTION_PORT = 30003
VISION_SERVER_PORT = 6005
GRIPPER_IO_INDEX = 1
VISION_TRIGGER_PORT = 6006 
VISION_TRIGGER_CMD = "TRIGGER"

def send_cmd(sock, cmd, log_prefix="CMD"):
    # (此函数无变化)
    try:
        full_cmd = cmd + "\n"
        print(f"[{log_prefix}] SND: {full_cmd.strip()}")
        sock.sendall(full_cmd.encode('utf-8'))
        response = sock.recv(1024).decode('utf-8').strip()
        print(f"[{log_prefix}] RCV: {response}")
        parts = response.split(',')
        error_id_str = parts[0]
        if error_id_str == '0': return True
        else:
            print(f"❌ 指令 '{cmd}' 失败，错误码: {error_id_str}")
            return False
    except socket.error as e:
        print(f"❌ 发送指令 '{cmd}' 时网络错误: {e}")
        return False

class RobotControlApp(ctk.CTk):
    def __init__(self):
        # (此函数无变化)
        super().__init__()
        self.title("机器人与传送带控制系统 (MG400) - V17.0")
        self.geometry("1200x900")  # 增大窗口尺寸以适应滚动内容
        self.current_conveyor_position = 0.0  # 当前逻辑位置（可被标定重置）
        self.absolute_conveyor_position = 0.0  # 绝对位置（从零开始，不受标定影响）
        self.conveyor_total_length = 1150.0
        self.dashboard_socket = None
        self.motion_socket = None
        self.vision_queue = queue.Queue()
        self.is_robot_connected = False

        # 初始化位置记忆管理器
        self.position_manager = ConveyorPositionManager(self)

        self.create_widgets()
        self.update_conveyor_display()
        self.vision_thread = threading.Thread(target=self.vision_listener_thread, daemon=True)
        self.vision_thread.start()
        self.process_vision_queue()
        self.protocol("WM_DELETE_WINDOW", self.on_closing)
        try:
            is_admin = os.getuid() == 0
        except AttributeError:
            is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
        if is_admin: self.log("✅ 程序正以【管理员权限】运行。", "lightgreen")
        else: self.log("⚠️ 程序正以【普通用户权限】运行。", "orange")

    def create_widgets(self):
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.pack(padx=10, pady=10, fill="both", expand=True)
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_columnconfigure(1, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)

        # 创建左侧滚动框架
        self.left_scroll_frame = ctk.CTkScrollableFrame(self.main_frame, label_text="控制面板")
        self.left_scroll_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        self.left_scroll_frame.grid_columnconfigure(0, weight=1)

        # 原来的left_frame现在作为滚动框架的内容
        self.left_frame = self.left_scroll_frame
        
        # ... (机器人人机控制UI代码无变化)
        manual_control_frame = ctk.CTkFrame(self.left_frame)
        manual_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(manual_control_frame, text="机器人人机控制", font=ctk.CTkFont(size=16, weight="bold")).grid(row=0, column=0, columnspan=2, pady=10)
        btn_x_plus = ctk.CTkButton(manual_control_frame, text="X+"); btn_x_minus = ctk.CTkButton(manual_control_frame, text="X-"); btn_y_plus = ctk.CTkButton(manual_control_frame, text="Y+"); btn_y_minus = ctk.CTkButton(manual_control_frame, text="Y-"); btn_z_plus = ctk.CTkButton(manual_control_frame, text="Z+"); btn_z_minus = ctk.CTkButton(manual_control_frame, text="Z-")
        btn_x_plus.grid(row=1, column=0, padx=5, pady=5, sticky="ew"); btn_x_minus.grid(row=1, column=1, padx=5, pady=5, sticky="ew"); btn_y_plus.grid(row=2, column=0, padx=5, pady=5, sticky="ew"); btn_y_minus.grid(row=2, column=1, padx=5, pady=5, sticky="ew"); btn_z_plus.grid(row=3, column=0, padx=5, pady=5, sticky="ew"); btn_z_minus.grid(row=3, column=1, padx=5, pady=5, sticky="ew")
        btn_x_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("X+")); btn_x_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("X-")); btn_y_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y+")); btn_y_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Y-")); btn_z_plus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z+")); btn_z_minus.bind("<ButtonPress-1>", lambda event: self.start_jog("Z-"))
        for btn in [btn_x_plus, btn_x_minus, btn_y_plus, btn_y_minus, btn_z_plus, btn_z_minus]: btn.bind("<ButtonRelease-1>", self.stop_jog)
        self.btn_home = ctk.CTkButton(manual_control_frame, text="回原点", command=self.go_home); self.btn_home.grid(row=4, column=0, columnspan=2, pady=10, padx=5, sticky="ew")

        # ▼▼▼▼▼ 【传送带控制UI - 重大更新】 ▼▼▼▼▼
        conveyor_frame = ctk.CTkFrame(self.left_frame)
        conveyor_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(conveyor_frame, text="传送带控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        
        # 逻辑位置显示
        conveyor_pos_frame = ctk.CTkFrame(conveyor_frame, fg_color="transparent")
        conveyor_pos_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(conveyor_pos_frame, text="当前逻辑位置:").pack(side="left")
        self.label_conveyor_pos = ctk.CTkLabel(conveyor_pos_frame, text="0.00 mm", font=ctk.CTkFont(size=14, weight="bold"))
        self.label_conveyor_pos.pack(side="left", padx=5)

        # 绝对位置显示
        absolute_pos_frame = ctk.CTkFrame(conveyor_frame, fg_color="transparent")
        absolute_pos_frame.pack(pady=5, padx=5, fill="x")
        ctk.CTkLabel(absolute_pos_frame, text="绝对位置(记录用):").pack(side="left")
        self.label_absolute_pos = ctk.CTkLabel(absolute_pos_frame, text="0.00 mm", font=ctk.CTkFont(size=14, weight="bold"), text_color="lightgreen")
        self.label_absolute_pos.pack(side="left", padx=5)

        self.progress_conveyor = ctk.CTkProgressBar(conveyor_frame)
        self.progress_conveyor.pack(pady=5, padx=5, fill="x")

        conveyor_input_frame = ctk.CTkFrame(conveyor_frame, fg_color="transparent")
        conveyor_input_frame.pack(pady=5, padx=5, fill="x")
        
        ctk.CTkLabel(conveyor_input_frame, text="步进距离(mm):").pack(side="left", padx=(0, 5))
        self.entry_conveyor_step_dist = ctk.CTkEntry(conveyor_input_frame, placeholder_text="例如: 50")
        self.entry_conveyor_step_dist.pack(side="left", fill="x", expand=True)
        # 预设一个默认值
        self.entry_conveyor_step_dist.insert(0, "50")

        conveyor_btn_frame = ctk.CTkFrame(conveyor_frame, fg_color="transparent")
        conveyor_btn_frame.pack(pady=5, padx=5, fill="x")
        
        self.btn_conveyor_fwd = ctk.CTkButton(conveyor_btn_frame, text="前进", command=lambda: self.move_conveyor_step(1))
        self.btn_conveyor_fwd.pack(side="left", fill="x", expand=True, padx=(0,5))
        
        self.btn_conveyor_bwd = ctk.CTkButton(conveyor_btn_frame, text="后退", command=lambda: self.move_conveyor_step(-1))
        self.btn_conveyor_bwd.pack(side="left", fill="x", expand=True, padx=(5,0))
        
        # 标定按钮框架
        calib_btn_frame = ctk.CTkFrame(conveyor_frame, fg_color="transparent")
        calib_btn_frame.pack(pady=10, padx=5, fill="x")

        self.btn_calib_conveyor = ctk.CTkButton(calib_btn_frame, text="标定逻辑零点", command=self.calibrate_conveyor_zero)
        self.btn_calib_conveyor.pack(side="left", fill="x", expand=True, padx=(0,5))

        self.btn_reset_absolute = ctk.CTkButton(calib_btn_frame, text="重置绝对零点", command=self.reset_absolute_position,
                                               fg_color="orange", hover_color="darkorange")
        self.btn_reset_absolute.pack(side="left", fill="x", expand=True, padx=(5,0))
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # ▼▼▼▼▼ 【滑轨位置记忆UI - V17.0新增】 ▼▼▼▼▼
        self.position_manager.create_position_ui(self.left_frame)
        # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲

        # ... (后续UI代码无变化)
        vision_control_frame = ctk.CTkFrame(self.left_frame); vision_control_frame.pack(pady=10, padx=10, fill="x")
        ctk.CTkLabel(vision_control_frame, text="视觉控制", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=10)
        ctk.CTkButton(vision_control_frame, text="拍照", command=self.trigger_vision_capture).pack(pady=5, padx=5, fill="x")
        connect_frame = ctk.CTkFrame(self.left_frame); connect_frame.pack(pady=20, padx=10, fill="x", side="bottom")
        self.connect_label = ctk.CTkLabel(connect_frame, text="机器人未连接", text_color="orange"); self.connect_label.pack(side="left", padx=10)
        self.btn_connect = ctk.CTkButton(connect_frame, text="连接机器人", command=self.handle_connect_button_click); self.btn_connect.pack(side="right", padx=10)
        self.auto_run_switch = ctk.CTkSwitch(connect_frame, text="自动抓取", onvalue=True, offvalue=False); self.auto_run_switch.pack(side="right", padx=10)

        # 创建右侧滚动框架
        self.right_scroll_frame = ctk.CTkScrollableFrame(self.main_frame, label_text="监控界面")
        self.right_scroll_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        self.right_scroll_frame.grid_columnconfigure(0, weight=1)

        # 图像显示区域
        self.image_display_label = ctk.CTkLabel(self.right_scroll_frame, text="[等待视觉软件发送图像...]", bg_color="grey30", height=300)
        self.image_display_label.pack(pady=10, padx=10, fill="x")

        # 日志显示区域
        ctk.CTkLabel(self.right_scroll_frame, text="信息显示/日志", font=ctk.CTkFont(size=14)).pack(pady=(20,5))
        self.log_textbox = ctk.CTkTextbox(self.right_scroll_frame, state="disabled", height=300)
        self.log_textbox.pack(pady=10, padx=10, fill="both", expand=True)


    # ▼▼▼▼▼ 【传送带控制函数 - 逻辑更新】 ▼▼▼▼▼
    def update_conveyor_display(self):
        """刷新传送带位置的UI显示"""
        # 更新逻辑位置显示
        self.label_conveyor_pos.configure(text=f"{self.current_conveyor_position:.2f} mm")
        # 更新绝对位置显示
        self.label_absolute_pos.configure(text=f"{self.absolute_conveyor_position:.2f} mm")
        # 进度条基于绝对位置计算
        progress = self.absolute_conveyor_position / self.conveyor_total_length if self.conveyor_total_length > 0 else 0
        self.progress_conveyor.set(progress)

    def calibrate_conveyor_zero(self):
        """将当前的逻辑位置重置为0，但不影响绝对位置"""
        self.log(f"⚙️ 逻辑零点已标定。绝对位置保持: {self.absolute_conveyor_position:.2f} mm", "yellow")
        self.current_conveyor_position = 0.0  # 只重置逻辑位置
        # 绝对位置不变，保持从真正零点开始的累积距离
        self.update_conveyor_display()

    def reset_absolute_position(self):
        """重置绝对位置为0（谨慎使用，会影响所有已记录位置）"""
        self.log("⚠️ 重置绝对零点。注意：这会影响所有已记录的位置！", "orange")
        self.absolute_conveyor_position = 0.0
        self.current_conveyor_position = 0.0  # 同时重置逻辑位置
        self.update_conveyor_display()

    def move_conveyor_step(self, direction):
        """按步进距离移动传送带 (1: 前进, -1: 后退)"""
        if not self.is_robot_connected:
            self.log("⚠️ 请先连接机器人", "orange")
            return
        
        step_dist_str = self.entry_conveyor_step_dist.get()
        try:
            step_distance = abs(float(step_dist_str)) # 确保步进值是正数
            distance_to_move = step_distance * direction # 乘以方向
            
            log_action = "前进" if direction > 0 else "后退"
            self.log(f" conveyor 准备{log_action} {step_distance} mm...")
            
            cmd = f"MovJExt({distance_to_move},{{SYNC=1}})"
            
            if send_cmd(self.motion_socket, cmd, "MOT"):
                # 同时更新逻辑位置和绝对位置
                self.current_conveyor_position += distance_to_move
                self.absolute_conveyor_position += distance_to_move
                self.update_conveyor_display()
                self.log(f"✅ 传送带移动完成。逻辑位置: {self.current_conveyor_position:.2f} mm, 绝对位置: {self.absolute_conveyor_position:.2f} mm", "green")
            else:
                self.log("❌ 传送带移动失败。", "red")

        except ValueError:
            self.log(f"❌ 无效输入: '{step_dist_str}' 不是一个有效的步进距离。", "red")
        except Exception as e:
            self.log(f"❌ 移动传送带时发生错误: {e}", "red")
    # ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲
    
    # (其他函数无变化...)
    def log(self, message, color="white"):
        self.log_textbox.configure(state="normal"); self.log_textbox.tag_config(f"tag_{color}", foreground=color); self.log_textbox.insert("end", f"{message}\n", f"tag_{color}"); self.log_textbox.configure(state="disabled"); self.log_textbox.see("end")
    def handle_connect_button_click(self):
        if not self.is_robot_connected:
            try:
                self.log(f"正在连接机器人 at {ROBOT_IP}...", "cyan"); self.dashboard_socket = socket.create_connection((ROBOT_IP, DASHBOARD_PORT), timeout=5); self.motion_socket = socket.create_connection((ROBOT_IP, MOTION_PORT), timeout=5)
                self.log("正在尝试使能机器人...", "yellow")
                if not send_cmd(self.dashboard_socket, "EnableRobot()", "DASH"): raise ConnectionError("机器人使能失败")
                self.log("机器人已使能，等待伺服系统稳定...", "yellow"); time.sleep(1)
                self.is_robot_connected = True; self.log("✅ 机器人连接并使能成功!", "green"); self.connect_label.configure(text="机器人已连接", text_color="green"); self.btn_connect.configure(text="断开连接")
            except Exception as e:
                self.log(f"❌ 连接失败: {e}", "red")
                if self.dashboard_socket: self.dashboard_socket.close()
                if self.motion_socket: self.motion_socket.close()
        else:
            send_cmd(self.dashboard_socket, "DisableRobot()", "DASH");
            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close();
            self.is_robot_connected = False; self.log("🔌 机器人已断开。", "orange"); self.connect_label.configure(text="机器人未连接", text_color="orange"); self.btn_connect.configure(text="连接机器人")
    def trigger_vision_capture(self):
        self.log("📸 发送拍照触发指令...", "yellow")
        try:
            with socket.create_connection((PYTHON_PC_IP, VISION_TRIGGER_PORT), timeout=3) as s:
                cmd_to_send = VISION_TRIGGER_CMD + "\n"; s.sendall(cmd_to_send.encode('utf-8')); self.log("✅ 触发指令已发送成功。", "green")
        except socket.timeout: self.log(f"❌ 触发失败: 连接视觉软件({PYTHON_PC_IP}:{VISION_TRIGGER_PORT})超时。", "red")
        except Exception as e: self.log(f"❌ 触发失败: {e}", "red")
    def start_jog(self, axis_id):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log(f"🤖 开始点动: {axis_id}", "cyan"); send_cmd(self.motion_socket, f"MoveJog({axis_id})", "MOT")
    def stop_jog(self, event=None):
        if not self.is_robot_connected: return
        self.log("🤖 停止点动", "cyan"); send_cmd(self.motion_socket, "MoveJog()", "MOT"); time.sleep(0.2)
    def go_home(self):
        if not self.is_robot_connected: self.log("⚠️ 请先连接机器人", "orange"); return
        self.log("🤖 正在移动到安全原点...");
        if send_cmd(self.motion_socket, "MoveJ(200, 0, 50, 0)", "MOT"):
            send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("✅ 已到达原点。")
        else: self.log("❌ 回原点失败。", "red")
    def execute_pick_and_place(self, target_x, target_y, target_r):
        if not self.is_robot_connected: self.log("⚠️ 自动抓取失败：机器人未连接", "orange"); return
        self.log(f"🤖 开始执行抓取任务..."); pickup_z_high, pickup_z_low = 50, 10; place_x, place_y, place_z = 150, -150, 50
        try:
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT"); send_cmd(self.motion_socket, f"MoveJ({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_low}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("🤏 抓取: 闭合夹爪", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 1)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH") 
            send_cmd(self.motion_socket, f"MoveL({target_x}, {target_y}, {pickup_z_high}, {target_r})", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            send_cmd(self.motion_socket, f"MoveJ({place_x}, {place_y}, {place_z}, 0)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH"); self.log("👐 放置: 张开夹爪", "cyan")
            send_cmd(self.motion_socket, f"DO({GRIPPER_IO_INDEX}, 0)", "MOT"); send_cmd(self.dashboard_socket, "Sync()", "DASH")
            self.go_home(); self.log("✅ 抓取任务完成!", "green")
        except Exception as e: self.log(f"❌ 机器人执行动作时出错: {e}", "red")
    def process_vision_queue(self):
        try:
            while not self.vision_queue.empty():
                message = self.vision_queue.get_nowait()
                self.log(f"📩 收到视觉数据包: {message}", "cyan")
                parts = message.split(';')
                image_path = parts[-1].strip()
                self.show_image_from_path(image_path)
                if len(parts) >= 2 and self.auto_run_switch.get() and self.is_robot_connected:
                    coord_data = parts[0]
                    try:
                        coord_parts = coord_data.split(',');
                        if len(coord_parts) >= 2:
                            robot_x, robot_y = float(coord_parts[0]), float(coord_parts[1]); robot_r = float(coord_parts[2]) if len(coord_parts) > 2 else 0.0
                            self.execute_pick_and_place(robot_x, robot_y, robot_r)
                        else: self.log(f"⚠️ 坐标部分格式无法解析: {coord_data}", "orange")
                    except (ValueError, IndexError) as e: self.log(f"❌ 解析坐标数据失败: {e}", "red")
        except queue.Empty: pass
        self.after(100, self.process_vision_queue)
    def show_image_from_path(self, image_path):
        max_retries = 5; retry_delay = 0.2
        for attempt in range(max_retries):
            if os.path.exists(image_path):
                try:
                    with Image.open(image_path) as image: image.verify()
                    with Image.open(image_path) as image:
                        image.thumbnail((self.image_display_label.winfo_width(), self.image_display_label.winfo_height()), Image.Resampling.LANCZOS)
                        ctk_image = ImageTk.PhotoImage(image)
                        self.image_display_label.configure(image=ctk_image, text=""); self.image_display_label.image = ctk_image
                        self.log(f"✅ 图像显示成功。(尝试第 {attempt + 1} 次)", "green"); return
                except (IOError, SyntaxError) as e: self.log(f"   - 第 {attempt + 1} 次尝试：文件不完整 ({e})，稍后重试...", "yellow"); time.sleep(retry_delay)
                except PermissionError: self.log(f"   - 第 {attempt + 1} 次尝试：文件被占用，稍后重试...", "yellow"); time.sleep(retry_delay)
                except Exception as e: self.log(f"❌ 显示图像时发生未知错误: {e}", "red"); return
            else: self.log(f"❌ 找不到图像文件: {image_path}", "red"); return
        self.log(f"❌ 图像加载失败：在 {max_retries} 次尝试后依然无法读取文件。", "red")
    def vision_listener_thread(self):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            try:
                s.bind(('0.0.0.0', VISION_SERVER_PORT)); s.listen()
                print(f"👂 视觉服务器已启动，正在监听端口 {VISION_SERVER_PORT}...")
                while True:
                    conn, addr = s.accept()
                    with conn:
                        print(f"🤝 视觉软件已连接: {addr}")
                        while True:
                            data = conn.recv(1024)
                            if not data: print("🔌 视觉软件已断开。"); break
                            self.vision_queue.put(data.decode('utf-8').strip())
            except OSError as e: print(f"❌ 端口 {VISION_SERVER_PORT} 绑定失败: {e}")
    def on_closing(self):
        if self.is_robot_connected:
            self.log("正在断开机器人连接..."); send_cmd(self.dashboard_socket, "DisableRobot()", "DASH")
            if self.dashboard_socket: self.dashboard_socket.close();
            if self.motion_socket: self.motion_socket.close()
        self.destroy()

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    app = RobotControlApp()
    app.mainloop()