#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试绝对位置系统功能
演示逻辑位置和绝对位置的区别
"""

import customtkinter as ctk
from 门 import ConveyorPositionManager

class MockRobotApp:
    """模拟主程序类，用于测试绝对位置系统"""
    
    def __init__(self):
        self.current_conveyor_position = 0.0  # 逻辑位置（可被标定重置）
        self.absolute_conveyor_position = 0.0  # 绝对位置（从零开始，不受标定影响）
        self.is_robot_connected = True  # 模拟已连接状态
        self.motion_socket = None  # 模拟socket
        
    def log(self, message, color="white"):
        """模拟日志输出"""
        print(f"[{color}] {message}")
        
    def update_conveyor_display(self):
        """模拟更新显示"""
        print(f"逻辑位置: {self.current_conveyor_position:.2f} mm, 绝对位置: {self.absolute_conveyor_position:.2f} mm")

def test_absolute_position_system():
    """测试绝对位置系统"""
    
    # 创建测试窗口
    root = ctk.CTk()
    root.title("绝对位置系统测试 - V17.0")
    root.geometry("800x700")
    
    # 创建模拟主程序
    mock_app = MockRobotApp()
    
    # 创建位置管理器
    position_manager = ConveyorPositionManager(mock_app)
    
    # 主框架
    main_frame = ctk.CTkFrame(root)
    main_frame.pack(padx=10, pady=10, fill="both", expand=True)
    
    # 位置显示区域
    display_frame = ctk.CTkFrame(main_frame)
    display_frame.pack(pady=10, padx=10, fill="x")
    
    ctk.CTkLabel(display_frame, text="位置显示", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=5)
    
    logic_pos_label = ctk.CTkLabel(display_frame, text="逻辑位置: 0.00 mm", font=ctk.CTkFont(size=14))
    logic_pos_label.pack(pady=2)
    
    absolute_pos_label = ctk.CTkLabel(display_frame, text="绝对位置: 0.00 mm", font=ctk.CTkFont(size=14), text_color="lightgreen")
    absolute_pos_label.pack(pady=2)
    
    # 控制按钮区域
    control_frame = ctk.CTkFrame(main_frame)
    control_frame.pack(pady=10, padx=10, fill="x")
    
    ctk.CTkLabel(control_frame, text="移动控制", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
    
    def simulate_move(distance):
        """模拟滑轨移动"""
        mock_app.current_conveyor_position += distance
        mock_app.absolute_conveyor_position += distance
        update_display()
        mock_app.log(f"移动 {distance} mm，新位置 - 逻辑: {mock_app.current_conveyor_position:.2f}, 绝对: {mock_app.absolute_conveyor_position:.2f}")
    
    def calibrate_logic_zero():
        """标定逻辑零点"""
        mock_app.log(f"标定逻辑零点，绝对位置保持: {mock_app.absolute_conveyor_position:.2f} mm", "yellow")
        mock_app.current_conveyor_position = 0.0
        update_display()
    
    def reset_absolute_zero():
        """重置绝对零点"""
        mock_app.log("重置绝对零点，所有记录位置将失效！", "orange")
        mock_app.absolute_conveyor_position = 0.0
        mock_app.current_conveyor_position = 0.0
        update_display()
    
    def update_display():
        """更新位置显示"""
        logic_pos_label.configure(text=f"逻辑位置: {mock_app.current_conveyor_position:.2f} mm")
        absolute_pos_label.configure(text=f"绝对位置: {mock_app.absolute_conveyor_position:.2f} mm")
    
    # 移动按钮
    move_btn_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
    move_btn_frame.pack(pady=5, fill="x")
    
    ctk.CTkButton(move_btn_frame, text="前进50mm", command=lambda: simulate_move(50)).pack(side="left", padx=5)
    ctk.CTkButton(move_btn_frame, text="后退50mm", command=lambda: simulate_move(-50)).pack(side="left", padx=5)
    ctk.CTkButton(move_btn_frame, text="前进100mm", command=lambda: simulate_move(100)).pack(side="left", padx=5)
    
    # 标定按钮
    calib_btn_frame = ctk.CTkFrame(control_frame, fg_color="transparent")
    calib_btn_frame.pack(pady=5, fill="x")
    
    ctk.CTkButton(calib_btn_frame, text="标定逻辑零点", command=calibrate_logic_zero).pack(side="left", padx=5)
    ctk.CTkButton(calib_btn_frame, text="重置绝对零点", command=reset_absolute_zero, 
                 fg_color="orange", hover_color="darkorange").pack(side="left", padx=5)
    
    # 位置记忆UI
    position_manager.create_position_ui(main_frame)
    
    # 说明文本
    info_frame = ctk.CTkFrame(main_frame)
    info_frame.pack(pady=10, padx=10, fill="both", expand=True)
    
    ctk.CTkLabel(info_frame, text="使用说明", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
    
    info_text = """
测试步骤：
1. 使用移动按钮移动滑轨到某个位置（如前进150mm）
2. 点击"记录"按钮记录位置（记录的是绝对位置）
3. 点击"标定逻辑零点"（注意绝对位置不变）
4. 继续移动滑轨到其他位置
5. 点击"移动到此"按钮，观察是否能正确回到记录位置

关键特点：
- 逻辑位置：可以被标定重置，用于日常操作
- 绝对位置：从零开始累积，不受标定影响，用于位置记录
- 位置记录基于绝对位置，确保标定后仍能正确定位
    """
    
    info_textbox = ctk.CTkTextbox(info_frame, height=200)
    info_textbox.pack(pady=5, padx=5, fill="both", expand=True)
    info_textbox.insert("0.0", info_text)
    info_textbox.configure(state="disabled")
    
    # 初始显示更新
    update_display()
    
    # 运行测试
    root.mainloop()

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    test_absolute_position_system()
