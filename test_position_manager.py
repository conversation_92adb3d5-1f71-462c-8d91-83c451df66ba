#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试滑轨位置记忆功能
"""

import customtkinter as ctk
from 门 import ConveyorPositionManager

class MockRobotApp:
    """模拟主程序类，用于测试位置管理器"""
    
    def __init__(self):
        self.current_conveyor_position = 0.0
        self.is_robot_connected = True  # 模拟已连接状态
        self.motion_socket = None  # 模拟socket
        
    def log(self, message, color="white"):
        """模拟日志输出"""
        print(f"[{color}] {message}")
        
    def update_conveyor_display(self):
        """模拟更新显示"""
        print(f"当前位置: {self.current_conveyor_position:.2f} mm")

def test_position_manager():
    """测试位置管理器功能"""
    
    # 创建测试窗口
    root = ctk.CTk()
    root.title("滑轨位置记忆功能测试")
    root.geometry("600x500")
    
    # 创建模拟主程序
    mock_app = MockRobotApp()
    
    # 创建位置管理器
    position_manager = ConveyorPositionManager(mock_app)
    
    # 创建UI
    position_manager.create_position_ui(root)
    
    # 添加一些测试按钮
    test_frame = ctk.CTkFrame(root)
    test_frame.pack(pady=10, padx=10, fill="x")
    
    ctk.CTkLabel(test_frame, text="测试控制", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=5)
    
    def simulate_move(distance):
        """模拟滑轨移动"""
        mock_app.current_conveyor_position += distance
        mock_app.update_conveyor_display()
    
    btn_frame = ctk.CTkFrame(test_frame, fg_color="transparent")
    btn_frame.pack(pady=5, fill="x")
    
    ctk.CTkButton(btn_frame, text="前进50mm", command=lambda: simulate_move(50)).pack(side="left", padx=5)
    ctk.CTkButton(btn_frame, text="后退50mm", command=lambda: simulate_move(-50)).pack(side="left", padx=5)
    ctk.CTkButton(btn_frame, text="归零", command=lambda: setattr(mock_app, 'current_conveyor_position', 0.0)).pack(side="left", padx=5)
    
    # 显示当前位置
    pos_label = ctk.CTkLabel(test_frame, text="当前位置: 0.00 mm")
    pos_label.pack(pady=5)
    
    def update_pos_display():
        pos_label.configure(text=f"当前位置: {mock_app.current_conveyor_position:.2f} mm")
        root.after(100, update_pos_display)
    
    update_pos_display()
    
    # 运行测试
    root.mainloop()

if __name__ == "__main__":
    ctk.set_appearance_mode("dark")
    ctk.set_default_color_theme("blue")
    test_position_manager()
